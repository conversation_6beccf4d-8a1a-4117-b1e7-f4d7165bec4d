on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master
  workflow_dispatch:

jobs:
  lint:
    name: <PERSON><PERSON>
    runs-on: self-hosted-ghr
    steps:
    - uses: actions/checkout@v4
      with:
        submodules: false

    # Cache build tools to avoid downloading them each time
    - uses: actions/cache@v4
      with:
        path: build/cache
        key: ${{ runner.os }}-build-tools-cache-${{ hashFiles('build/checksums.txt') }}

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: 1.24
        cache: false

    - name: Run linters
      run: |
        go run build/ci.go lint
        go run build/ci.go check_generate
        go run build/ci.go check_baddeps

  test:
    name: Test
    needs: lint
    runs-on: self-hosted-ghr
    strategy:
      matrix:
        go:
          - '1.24'
          - '1.23'
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: ${{ matrix.go }}
          cache: false

      - name: Run tests
        run: go test ./...
