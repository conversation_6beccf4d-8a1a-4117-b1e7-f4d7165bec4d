// Code generated by github.com/fjl/gencodec. DO NOT EDIT.

package native

import (
	"encoding/json"
	"math/big"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/ethereum/go-ethereum/core/vm"
)

var _ = (*callFrameWithOpcodesMarshaling)(nil)

// MarshalJSON marshals as JSON.
func (c callFrameWithOpcodes) MarshalJSON() ([]byte, error) {
	type callFrameWithOpcodes0 struct {
		Type              vm.OpCode                                  `json:"-"`
		From              common.Address                             `json:"from"`
		Gas               hexutil.Uint64                             `json:"gas"`
		GasUsed           hexutil.Uint64                             `json:"gasUsed"`
		To                *common.Address                            `json:"to,omitempty" rlp:"optional"`
		Input             hexutil.Bytes                              `json:"input" rlp:"optional"`
		Output            hexutil.Bytes                              `json:"output,omitempty" rlp:"optional"`
		Error             string                                     `json:"error,omitempty" rlp:"optional"`
		RevertReason      string                                     `json:"revertReason,omitempty"`
		Logs              []callLog                                  `json:"logs,omitempty" rlp:"optional"`
		Value             *hexutil.Big                               `json:"value,omitempty" rlp:"optional"`
		AccessedSlots     accessedSlots                              `json:"accessedSlots"`
		ExtCodeAccessInfo []common.Address                           `json:"extCodeAccessInfo"`
		UsedOpcodes       map[hexutil.Uint64]uint64                  `json:"usedOpcodes"`
		ContractSize      map[common.Address]*contractSizeWithOpcode `json:"contractSize"`
		OutOfGas          bool                                       `json:"outOfGas"`
		KeccakPreimages   []hexutil.Bytes                            `json:"keccak,omitempty"`
		Calls             []callFrameWithOpcodes                     `json:"calls,omitempty" rlp:"optional"`
		TypeString        string                                     `json:"type"`
	}
	var enc callFrameWithOpcodes0
	enc.Type = c.Type
	enc.From = c.From
	enc.Gas = hexutil.Uint64(c.Gas)
	enc.GasUsed = hexutil.Uint64(c.GasUsed)
	enc.To = c.To
	enc.Input = c.Input
	enc.Output = c.Output
	enc.Error = c.Error
	enc.RevertReason = c.RevertReason
	enc.Logs = c.Logs
	enc.Value = (*hexutil.Big)(c.Value)
	enc.AccessedSlots = c.AccessedSlots
	enc.ExtCodeAccessInfo = c.ExtCodeAccessInfo
	if c.UsedOpcodes != nil {
		enc.UsedOpcodes = make(map[hexutil.Uint64]uint64, len(c.UsedOpcodes))
		for k, v := range c.UsedOpcodes {
			enc.UsedOpcodes[hexutil.Uint64(k)] = v
		}
	}
	enc.ContractSize = c.ContractSize
	enc.OutOfGas = c.OutOfGas
	if c.KeccakPreimages != nil {
		enc.KeccakPreimages = make([]hexutil.Bytes, len(c.KeccakPreimages))
		for k, v := range c.KeccakPreimages {
			enc.KeccakPreimages[k] = v
		}
	}
	enc.Calls = c.Calls
	enc.TypeString = c.TypeString()
	return json.Marshal(&enc)
}

// UnmarshalJSON unmarshals from JSON.
func (c *callFrameWithOpcodes) UnmarshalJSON(input []byte) error {
	type callFrameWithOpcodes0 struct {
		Type              *vm.OpCode                                 `json:"-"`
		From              *common.Address                            `json:"from"`
		Gas               *hexutil.Uint64                            `json:"gas"`
		GasUsed           *hexutil.Uint64                            `json:"gasUsed"`
		To                *common.Address                            `json:"to,omitempty" rlp:"optional"`
		Input             *hexutil.Bytes                             `json:"input" rlp:"optional"`
		Output            *hexutil.Bytes                             `json:"output,omitempty" rlp:"optional"`
		Error             *string                                    `json:"error,omitempty" rlp:"optional"`
		RevertReason      *string                                    `json:"revertReason,omitempty"`
		Logs              []callLog                                  `json:"logs,omitempty" rlp:"optional"`
		Value             *hexutil.Big                               `json:"value,omitempty" rlp:"optional"`
		AccessedSlots     *accessedSlots                             `json:"accessedSlots"`
		ExtCodeAccessInfo []common.Address                           `json:"extCodeAccessInfo"`
		UsedOpcodes       map[hexutil.Uint64]uint64                  `json:"usedOpcodes"`
		ContractSize      map[common.Address]*contractSizeWithOpcode `json:"contractSize"`
		OutOfGas          *bool                                      `json:"outOfGas"`
		KeccakPreimages   []hexutil.Bytes                            `json:"keccak,omitempty"`
		Calls             []callFrameWithOpcodes                     `json:"calls,omitempty" rlp:"optional"`
	}
	var dec callFrameWithOpcodes0
	if err := json.Unmarshal(input, &dec); err != nil {
		return err
	}
	if dec.Type != nil {
		c.Type = *dec.Type
	}
	if dec.From != nil {
		c.From = *dec.From
	}
	if dec.Gas != nil {
		c.Gas = uint64(*dec.Gas)
	}
	if dec.GasUsed != nil {
		c.GasUsed = uint64(*dec.GasUsed)
	}
	if dec.To != nil {
		c.To = dec.To
	}
	if dec.Input != nil {
		c.Input = *dec.Input
	}
	if dec.Output != nil {
		c.Output = *dec.Output
	}
	if dec.Error != nil {
		c.Error = *dec.Error
	}
	if dec.RevertReason != nil {
		c.RevertReason = *dec.RevertReason
	}
	if dec.Logs != nil {
		c.Logs = dec.Logs
	}
	if dec.Value != nil {
		c.Value = (*big.Int)(dec.Value)
	}
	if dec.AccessedSlots != nil {
		c.AccessedSlots = *dec.AccessedSlots
	}
	if dec.ExtCodeAccessInfo != nil {
		c.ExtCodeAccessInfo = dec.ExtCodeAccessInfo
	}
	if dec.UsedOpcodes != nil {
		c.UsedOpcodes = make(map[vm.OpCode]uint64, len(dec.UsedOpcodes))
		for k, v := range dec.UsedOpcodes {
			c.UsedOpcodes[vm.OpCode(k)] = v
		}
	}
	if dec.ContractSize != nil {
		c.ContractSize = dec.ContractSize
	}
	if dec.OutOfGas != nil {
		c.OutOfGas = *dec.OutOfGas
	}
	if dec.KeccakPreimages != nil {
		c.KeccakPreimages = make([][]byte, len(dec.KeccakPreimages))
		for k, v := range dec.KeccakPreimages {
			c.KeccakPreimages[k] = v
		}
	}
	if dec.Calls != nil {
		c.Calls = dec.Calls
	}
	return nil
}
