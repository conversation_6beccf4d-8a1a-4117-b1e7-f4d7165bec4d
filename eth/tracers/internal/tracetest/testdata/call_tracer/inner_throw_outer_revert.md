This test tests out the trace generated by the deployment of this contract:

```solidity
contract Revertor {
    function run() public pure {
        require(2 > 3, "This called failed");
    }
}

contract Contract {
    constructor() {
        Revertor r = new Revertor();
        r.run();
    }
}
```

The trace should show a revert, with the revert reason for both the top-call as well
as the inner call.
