on:
  push:
    tags:
      - "v*"
  workflow_dispatch:

  ### Note we cannot use cron-triggered builds right now, <PERSON><PERSON><PERSON> seems to have
  ### a few bugs in that area. So this workflow is scheduled using an external
  ### triggering mechanism and workflow_dispatch.
  #
  # schedule:
  #   - cron: '0 16 * * *'


jobs:
  ppa:
    name: PPA Upload
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Show environment
      run: |
        env

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: 1.24
        cache: false

    - name: Install deb toolchain
      run: |
        apt-get update
        apt-get -yq --no-install-suggests --no-install-recommends install devscripts debhelper dput fakeroot

    - name: Add launchpad to known_hosts
      run: |
        echo '|1|7SiYPr9xl3uctzovOTj4gMwAC1M=|t6ReES75Bo/PxlOPJ6/GsGbTrM0= ssh-rsa AAAAB3NzaC1yc2EAAAABIwAAAQEA0aKz5UTUndYgIGG7dQBV+HaeuEZJ2xPHo2DS2iSKvUL4xNMSAY4UguNW+pX56nAQmZKIZZ8MaEvSj6zMEDiq6HFfn5JcTlM80UwlnyKe8B8p7Nk06PPQLrnmQt5fh0HmEcZx+JU9TZsfCHPnX7MNz4ELfZE6cFsclClrKim3BHUIGq//t93DllB+h4O9LHjEUsQ1Sr63irDLSutkLJD6RXchjROXkNirlcNVHH/jwLWR5RcYilNX7S5bIkK8NlWPjsn/8Ua5O7I9/YoE97PpO6i73DTGLh5H9JN/SITwCKBkgSDWUt61uPK3Y11Gty7o2lWsBjhBUm2Y38CBsoGmBw==' >> ~/.ssh/known_hosts

    - name: Run ci.go
      run: |
        go run build/ci.go debsrc -upload ethereum/ethereum -sftp-user geth-ci -signer "Go Ethereum Linux Builder <<EMAIL>>"
      env:
        PPA_SIGNING_KEY: ${{ secrets.PPA_SIGNING_KEY }}
        PPA_SSH_KEY: ${{ secrets.PPA_SSH_KEY }}
