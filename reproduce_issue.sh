#!/bin/bash

# 复现 Issue #28838: eth_getFilterChanges 返回空输出
# 使用 curl 直接调用 RPC 接口

set -e

# 配置
RPC_URL="http://localhost:8545"
CONTRACT_ADDRESS="******************************************"
EVENT_TOPIC="0x8955a20de0ce0688e9ee8f31e787dfa365d9f8420c8565c945af9c18695469e0"

echo "🔍 复现 Issue #28838: eth_getFilterChanges 返回空输出"
echo "=================================================="

# 检查节点是否运行
echo "📡 检查 geth 节点连接..."
if ! curl -s -X POST -H "Content-Type: application/json" \
    --data '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' \
    $RPC_URL > /dev/null; then
    echo "❌ 无法连接到 geth 节点"
    echo "💡 请确保 geth 正在运行: geth --dev --http --http.api eth,web3,net"
    exit 1
fi
echo "✅ 节点连接正常"

# 1. 使用 eth_getLogs 查询历史日志
echo ""
echo "1️⃣ 使用 eth_getLogs 查询历史日志..."
GET_LOGS_RESPONSE=$(curl -s -X POST -H "Content-Type: application/json" \
    --data "{\"jsonrpc\":\"2.0\",\"method\":\"eth_getLogs\",\"params\":[{\"fromBlock\":\"earliest\",\"toBlock\":\"latest\",\"address\":\"$CONTRACT_ADDRESS\",\"topics\":[\"$EVENT_TOPIC\"]}],\"id\":1}" \
    $RPC_URL)

echo "📝 eth_getLogs 响应:"
echo "$GET_LOGS_RESPONSE" | jq '.'

# 提取日志数量
GET_LOGS_COUNT=$(echo "$GET_LOGS_RESPONSE" | jq '.result | length')
echo "📊 eth_getLogs 返回 $GET_LOGS_COUNT 条日志"

# 2. 创建过滤器
echo ""
echo "2️⃣ 创建过滤器 (fromBlock: earliest)..."
FILTER_RESPONSE=$(curl -s -X POST -H "Content-Type: application/json" \
    --data "{\"jsonrpc\":\"2.0\",\"method\":\"eth_newFilter\",\"params\":[{\"fromBlock\":\"earliest\",\"toBlock\":\"latest\",\"address\":\"$CONTRACT_ADDRESS\",\"topics\":[\"$EVENT_TOPIC\"]}],\"id\":2}" \
    $RPC_URL)

echo "📝 eth_newFilter 响应:"
echo "$FILTER_RESPONSE" | jq '.'

# 提取过滤器 ID
FILTER_ID=$(echo "$FILTER_RESPONSE" | jq -r '.result')
if [ "$FILTER_ID" = "null" ] || [ -z "$FILTER_ID" ]; then
    echo "❌ 创建过滤器失败"
    exit 1
fi
echo "✅ 过滤器创建成功，ID: $FILTER_ID"

# 3. 立即调用 getFilterChanges
echo ""
echo "3️⃣ 立即调用 eth_getFilterChanges..."
FILTER_CHANGES_RESPONSE=$(curl -s -X POST -H "Content-Type: application/json" \
    --data "{\"jsonrpc\":\"2.0\",\"method\":\"eth_getFilterChanges\",\"params\":[\"$FILTER_ID\"],\"id\":3}" \
    $RPC_URL)

echo "📝 eth_getFilterChanges 响应:"
echo "$FILTER_CHANGES_RESPONSE" | jq '.'

# 提取日志数量
FILTER_CHANGES_COUNT=$(echo "$FILTER_CHANGES_RESPONSE" | jq '.result | length')
echo "📊 eth_getFilterChanges 返回 $FILTER_CHANGES_COUNT 条日志"

# 4. 等待后再次调用
echo ""
echo "4️⃣ 等待 3 秒后再次调用 eth_getFilterChanges..."
sleep 3

FILTER_CHANGES_RESPONSE2=$(curl -s -X POST -H "Content-Type: application/json" \
    --data "{\"jsonrpc\":\"2.0\",\"method\":\"eth_getFilterChanges\",\"params\":[\"$FILTER_ID\"],\"id\":4}" \
    $RPC_URL)

echo "📝 第二次 eth_getFilterChanges 响应:"
echo "$FILTER_CHANGES_RESPONSE2" | jq '.'

FILTER_CHANGES_COUNT2=$(echo "$FILTER_CHANGES_RESPONSE2" | jq '.result | length')
echo "📊 第二次 eth_getFilterChanges 返回 $FILTER_CHANGES_COUNT2 条日志"

# 5. 结果对比
echo ""
echo "📊 结果对比:"
echo "=================================================="
echo "eth_getLogs:              $GET_LOGS_COUNT 条日志"
echo "eth_getFilterChanges(1):  $FILTER_CHANGES_COUNT 条日志"
echo "eth_getFilterChanges(2):  $FILTER_CHANGES_COUNT2 条日志"

if [ "$GET_LOGS_COUNT" -gt 0 ] && [ "$FILTER_CHANGES_COUNT" -eq 0 ]; then
    echo ""
    echo "🐛 问题确认: API 行为不一致！"
    echo "   - eth_getLogs 可以获取历史日志"
    echo "   - eth_getFilterChanges 无法获取历史日志"
    echo "   - 使用相同的参数 fromBlock: 'earliest'"
    echo ""
    echo "🎯 这就是 Issue #28838 描述的问题"
elif [ "$GET_LOGS_COUNT" -eq 0 ]; then
    echo ""
    echo "⚠️  没有历史日志可供测试"
    echo "💡 建议:"
    echo "   1. 部署测试合约"
    echo "   2. 触发一些事件"
    echo "   3. 重新运行此脚本"
else
    echo ""
    echo "✅ 行为一致（可能问题已修复或测试条件不满足）"
fi

# 6. 清理过滤器
echo ""
echo "🧹 清理过滤器..."
curl -s -X POST -H "Content-Type: application/json" \
    --data "{\"jsonrpc\":\"2.0\",\"method\":\"eth_uninstallFilter\",\"params\":[\"$FILTER_ID\"],\"id\":5}" \
    $RPC_URL > /dev/null

echo "✅ 测试完成"
