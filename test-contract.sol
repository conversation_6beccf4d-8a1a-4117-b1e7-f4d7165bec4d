// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * 用于测试 Issue #28838 的简单合约
 * 部署后可以触发事件来复现过滤器问题
 */
contract TestEventContract {
    // 测试事件 - 与 issue 中使用的事件签名匹配
    event TestEvent(
        address indexed sender,
        uint256 indexed value,
        string message
    );

    // 计数器
    uint256 public eventCount;

    // 构造函数
    constructor() {
        eventCount = 0;
    }

    /**
     * 触发测试事件
     * @param value 测试值
     * @param message 测试消息
     */
    function emitTestEvent(uint256 value, string memory message) public {
        eventCount++;
        emit TestEvent(msg.sender, value, message);
    }

    /**
     * 批量触发事件（用于测试多个日志）
     * @param count 事件数量
     */
    function emitMultipleEvents(uint256 count) public {
        for (uint256 i = 0; i < count; i++) {
            eventCount++;
            emit TestEvent(
                msg.sender,
                i,
                string(abi.encodePacked("Event #", toString(i)))
            );
        }
    }

    /**
     * 获取事件总数
     */
    function getEventCount() public view returns (uint256) {
        return eventCount;
    }

    /**
     * 辅助函数：将 uint256 转换为 string
     */
    function toString(uint256 value) internal pure returns (string memory) {
        if (value == 0) {
            return "0";
        }
        uint256 temp = value;
        uint256 digits;
        while (temp != 0) {
            digits++;
            temp /= 10;
        }
        bytes memory buffer = new bytes(digits);
        while (value != 0) {
            digits -= 1;
            buffer[digits] = bytes1(uint8(48 + uint256(value % 10)));
            value /= 10;
        }
        return string(buffer);
    }
}
