#!/usr/bin/env node

/**
 * 复现 Issue #28838: eth_getFilterChanges 返回空输出
 * 
 * 这个脚本演示了 eth_newFilter + eth_getFilterChanges 与 eth_getLogs 的不一致行为
 */

const { Web3 } = require('web3');

// 配置
const RPC_URL = 'http://localhost:8545';
const CONTRACT_ADDRESS = '******************************************'; // 示例地址
const EVENT_TOPIC = '0x8955a20de0ce0688e9ee8f31e787dfa365d9f8420c8565c945af9c18695469e0'; // 示例主题

async function main() {
    const web3 = new Web3(RPC_URL);
    
    console.log('🔍 复现 Issue #28838: eth_getFilterChanges 返回空输出\n');
    
    try {
        // 1. 首先使用 eth_getLogs 查询历史日志（作为对比）
        console.log('1️⃣ 使用 eth_getLogs 查询历史日志...');
        const getLogsResult = await web3.eth.getPastLogs({
            fromBlock: 'earliest',
            toBlock: 'latest',
            address: CONTRACT_ADDRESS,
            topics: [EVENT_TOPIC]
        });
        
        console.log(`   ✅ eth_getLogs 返回 ${getLogsResult.length} 条日志`);
        if (getLogsResult.length > 0) {
            console.log(`   📝 第一条日志: 区块 ${getLogsResult[0].blockNumber}`);
        }
        
        // 2. 创建过滤器
        console.log('\n2️⃣ 创建过滤器 (fromBlock: earliest)...');
        const filterResult = await web3.eth.requestManager.send({
            method: 'eth_newFilter',
            params: [{
                fromBlock: 'earliest',
                toBlock: 'latest',
                address: CONTRACT_ADDRESS,
                topics: [EVENT_TOPIC]
            }]
        });
        
        const filterId = filterResult;
        console.log(`   ✅ 过滤器创建成功，ID: ${filterId}`);
        
        // 3. 立即调用 getFilterChanges
        console.log('\n3️⃣ 立即调用 eth_getFilterChanges...');
        const filterChangesResult = await web3.eth.requestManager.send({
            method: 'eth_getFilterChanges',
            params: [filterId]
        });
        
        console.log(`   ❌ eth_getFilterChanges 返回 ${filterChangesResult.length} 条日志`);
        
        // 4. 等待一段时间后再次调用
        console.log('\n4️⃣ 等待 5 秒后再次调用 eth_getFilterChanges...');
        await sleep(5000);
        
        const filterChangesResult2 = await web3.eth.requestManager.send({
            method: 'eth_getFilterChanges',
            params: [filterId]
        });
        
        console.log(`   ❌ eth_getFilterChanges 返回 ${filterChangesResult2.length} 条日志`);
        
        // 5. 对比结果
        console.log('\n📊 结果对比:');
        console.log(`   eth_getLogs:          ${getLogsResult.length} 条日志`);
        console.log(`   eth_getFilterChanges: ${filterChangesResult.length} 条日志`);
        
        if (getLogsResult.length > 0 && filterChangesResult.length === 0) {
            console.log('\n🐛 问题确认: API 行为不一致！');
            console.log('   - eth_getLogs 可以获取历史日志');
            console.log('   - eth_getFilterChanges 无法获取历史日志');
            console.log('   - 使用相同的参数 fromBlock: "earliest"');
        } else if (getLogsResult.length === 0) {
            console.log('\n⚠️  没有历史日志可供测试');
            console.log('   请先部署合约并触发一些事件');
        } else {
            console.log('\n✅ 行为一致（可能问题已修复）');
        }
        
        // 6. 清理过滤器
        await web3.eth.requestManager.send({
            method: 'eth_uninstallFilter',
            params: [filterId]
        });
        
    } catch (error) {
        console.error('❌ 错误:', error.message);
        
        if (error.message.includes('connection')) {
            console.log('\n💡 提示: 请确保 geth 节点正在运行:');
            console.log('   geth --dev --http --http.api eth,web3,net');
        }
    }
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// 如果直接运行此脚本
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { main };
