{"types": {"EIP712Domain": [{"name": "name", "type": "string"}, {"name": "version", "type": "string"}, {"name": "chainId", "type": "uint256"}, {"name": "verifyingContract", "type": "address"}], "Person": [{"name": "name", "type": "string"}], "Mail": [{"name": "from", "type": "Person"}, {"name": "to", "type": "Person[]"}, {"name": "contents", "type": "string"}]}, "primaryType": "Mail", "domain": {"name": "Ether Mail", "version": "1", "chainId": "1", "verifyingContract": "******************************************"}, "message": {"from": {"name": "Cow"}, "to": [{"name": "<PERSON>"}, {"name": "Goose"}], "contents": "Hello, <PERSON>!"}}