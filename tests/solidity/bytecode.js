// Copyright 2019 The go-ethereum Authors
// This file is part of the go-ethereum library.
//
// The go-ethereum library is free software: you can redistribute it and/or modify
// it under the terms of the GNU Lesser General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// The go-ethereum library is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
// GNU Lesser General Public License for more details.
//
// You should have received a copy of the GNU Lesser General Public License
// along with the go-ethereum library. If not, see <http://www.gnu.org/licenses/>.

{
	"linkReferences": {},
	"object": "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",
	"opcodes": "PUSH1 0x80 PUSH1 0x40 MSTORE CALLVALUE DUP1 ISZERO PUSH2 0x10 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x19 PUSH2 0x7A JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 PUSH1 0x0 CREATE DUP1 ISZERO DUP1 ISZERO PUSH2 0x35 JUMPI RETURNDATASIZE PUSH1 0x0 DUP1 RETURNDATACOPY RETURNDATASIZE PUSH1 0x0 REVERT JUMPDEST POP PUSH1 0x0 DUP1 PUSH2 0x100 EXP DUP2 SLOAD DUP2 PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF MUL NOT AND SWAP1 DUP4 PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF AND MUL OR SWAP1 SSTORE POP PUSH2 0x8A JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH2 0x15F DUP1 PUSH2 0x55C DUP4 CODECOPY ADD SWAP1 JUMP JUMPDEST PUSH2 0x4C3 DUP1 PUSH2 0x99 PUSH1 0x0 CODECOPY PUSH1 0x0 RETURN INVALID PUSH1 0x80 PUSH1 0x40 MSTORE PUSH1 0x4 CALLDATASIZE LT PUSH2 0x5C JUMPI PUSH1 0x0 CALLDATALOAD PUSH29 0x100000000000000000000000000000000000000000000000000000000 SWAP1 DIV DUP1 PUSH4 0x55313DEA EQ PUSH2 0x61 JUMPI DUP1 PUSH4 0x6D3D1416 EQ PUSH2 0x78 JUMPI DUP1 PUSH4 0xB9D1E5AA EQ PUSH2 0x8F JUMPI DUP1 PUSH4 0xF8A8FD6D EQ PUSH2 0xA6 JUMPI JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x6D JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x76 PUSH2 0xBD JUMP JUMPDEST STOP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x84 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x8D PUSH2 0xBF JUMP JUMPDEST STOP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x9B JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0xA4 PUSH2 0xC4 JUMP JUMPDEST STOP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0xB2 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0xBB PUSH2 0xC6 JUMP JUMPDEST STOP JUMPDEST STOP JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST INVALID JUMPDEST PUSH1 0x1 PUSH1 0x2 BYTE PUSH1 0x2 RETURN JUMPDEST PUSH1 0x5 DUP2 LT ISZERO PUSH2 0xE3 JUMPI PUSH1 0x1 DUP2 ADD SWAP1 POP PUSH2 0xCF JUMP JUMPDEST POP PUSH1 0x6 JUMPDEST PUSH1 0x5 DUP2 GT ISZERO PUSH2 0xFB JUMPI PUSH1 0x1 DUP2 ADD SWAP1 POP PUSH2 0xE7 JUMP JUMPDEST POP PUSH1 0x1 JUMPDEST PUSH1 0x5 DUP2 SLT ISZERO PUSH2 0x113 JUMPI PUSH1 0x1 DUP2 ADD SWAP1 POP PUSH2 0xFF JUMP JUMPDEST POP PUSH1 0x6 JUMPDEST PUSH1 0x5 DUP2 SGT ISZERO PUSH2 0x12B JUMPI PUSH1 0x1 DUP2 ADD SWAP1 POP PUSH2 0x117 JUMP JUMPDEST POP PUSH1 0x2 ISZERO PUSH2 0x138 JUMPI PUSH1 0x0 MLOAD POP JUMPDEST PUSH1 0x40 MLOAD PUSH1 0x20 DUP2 ADD PUSH1 0x20 PUSH1 0x4 DUP4 CALLDATACOPY POP POP PUSH1 0x40 MLOAD PUSH1 0x20 DUP2 ADD PUSH1 0x20 PUSH1 0x4 DUP4 CODECOPY POP POP PUSH1 0x40 MLOAD PUSH1 0x20 DUP2 ADD PUSH1 0x20 PUSH1 0x4 DUP4 PUSH1 0x0 EXTCODECOPY POP POP CALLDATASIZE PUSH1 0x0 JUMPDEST DUP2 DUP2 LT ISZERO PUSH2 0x182 JUMPI PUSH1 0x2 DUP2 MSTORE PUSH1 0x1 DUP2 ADD SWAP1 POP PUSH2 0x16B JUMP JUMPDEST POP POP PUSH1 0x0 DUP1 KECCAK256 POP PUSH1 0x0 PUSH1 0x20 PUSH1 0x40 RETURNDATACOPY PUSH1 0x10 PUSH1 0x80 PUSH1 0x10 PUSH1 0x40 PUSH2 0x123 PUSH2 0x2710 STATICCALL POP PUSH1 0x20 PUSH2 0x123 PUSH1 0xA CREATE POP PUSH1 0x0 DUP1 PUSH1 0x0 SWAP1 SLOAD SWAP1 PUSH2 0x100 EXP SWAP1 DIV PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF AND SWAP1 POP PUSH1 0x0 PUSH1 0x40 MLOAD DUP1 DUP1 PUSH32 0x697353616D654164647265737328616464726573732C61646472657373290000 DUP2 MSTORE POP PUSH1 0x1E ADD SWAP1 POP PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 KECCAK256 SWAP1 POP PUSH1 0x0 CALLER SWAP1 POP PUSH1 0x40 MLOAD DUP3 DUP2 MSTORE DUP2 PUSH1 0x4 DUP3 ADD MSTORE DUP2 PUSH1 0x24 DUP3 ADD MSTORE PUSH1 0x64 DUP2 ADD PUSH1 0x40 MSTORE PUSH1 0x20 DUP2 PUSH1 0x44 DUP4 PUSH1 0x0 DUP9 PUSH2 0x1388 CALL POP POP PUSH1 0x40 MLOAD DUP3 DUP2 MSTORE DUP2 PUSH1 0x4 DUP3 ADD MSTORE DUP2 PUSH1 0x24 DUP3 ADD MSTORE PUSH1 0x64 DUP2 ADD PUSH1 0x40 MSTORE PUSH1 0x20 DUP2 PUSH1 0x44 DUP4 PUSH1 0x0 DUP9 PUSH2 0x1388 CALLCODE POP POP PUSH1 0x40 MLOAD DUP3 DUP2 MSTORE DUP2 PUSH1 0x4 DUP3 ADD MSTORE DUP2 PUSH1 0x24 DUP3 ADD MSTORE PUSH1 0x64 DUP2 ADD PUSH1 0x40 MSTORE PUSH1 0x20 DUP2 PUSH1 0x44 DUP4 DUP8 PUSH2 0x1388 DELEGATECALL POP POP PUSH1 0x0 PUSH3 0x420042 SWAP1 POP PUSH32 0x50CB9FE53DAA9737B786AB3646F04D0150DC50EF4E75F59509D83667AD5ADB20 PUSH1 0x1 MUL PUSH1 0x40 MLOAD DUP1 DUP3 DUP2 MSTORE PUSH1 0x20 ADD SWAP2 POP POP PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG0 PUSH32 0x50CB9FE53DAA9737B786AB3646F04D0150DC50EF4E75F59509D83667AD5ADB20 PUSH1 0x1 MUL PUSH32 0x50CB9FE53DAA9737B786AB3646F04D0150DC50EF4E75F59509D83667AD5ADB20 PUSH1 0x1 MUL PUSH1 0x40 MLOAD DUP1 DUP3 DUP2 MSTORE PUSH1 0x20 ADD SWAP2 POP POP PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG1 CALLER PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF AND PUSH1 0x1 MUL PUSH32 0x50CB9FE53DAA9737B786AB3646F04D0150DC50EF4E75F59509D83667AD5ADB20 PUSH1 0x1 MUL PUSH32 0x50CB9FE53DAA9737B786AB3646F04D0150DC50EF4E75F59509D83667AD5ADB20 PUSH1 0x1 MUL PUSH1 0x40 MLOAD DUP1 DUP3 DUP2 MSTORE PUSH1 0x20 ADD SWAP2 POP POP PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG2 DUP1 PUSH1 0x1 MUL CALLER PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF AND PUSH1 0x1 MUL PUSH32 0x50CB9FE53DAA9737B786AB3646F04D0150DC50EF4E75F59509D83667AD5ADB20 PUSH1 0x1 MUL PUSH32 0x50CB9FE53DAA9737B786AB3646F04D0150DC50EF4E75F59509D83667AD5ADB20 PUSH1 0x1 MUL PUSH1 0x40 MLOAD DUP1 DUP3 DUP2 MSTORE PUSH1 0x20 ADD SWAP2 POP POP PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG3 DUP1 PUSH1 0x1 MUL DUP2 PUSH1 0x1 MUL CALLER PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF AND PUSH1 0x1 MUL PUSH32 0x50CB9FE53DAA9737B786AB3646F04D0150DC50EF4E75F59509D83667AD5ADB20 PUSH1 0x1 MUL PUSH32 0x50CB9FE53DAA9737B786AB3646F04D0150DC50EF4E75F59509D83667AD5ADB20 PUSH1 0x1 MUL PUSH1 0x40 MLOAD DUP1 DUP3 DUP2 MSTORE PUSH1 0x20 ADD SWAP2 POP POP PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG4 PUSH1 0x2 SELFDESTRUCT INVALID LOG1 PUSH6 0x627A7A723058 KECCAK256 0xe MLOAD 0xba LOG2 0xb4 SLOAD 0xb4 PUSH32 0xDF0EF596FA24AFF8ED3A3727B7481EBD25349182CE7152A30029608060405234 DUP1 ISZERO PUSH2 0x10 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x13F DUP1 PUSH2 0x20 PUSH1 0x0 CODECOPY PUSH1 0x0 RETURN INVALID PUSH1 0x80 PUSH1 0x40 MSTORE PUSH1 0x4 CALLDATASIZE LT PUSH2 0x3B JUMPI PUSH1 0x0 CALLDATALOAD PUSH29 0x100000000000000000000000000000000000000000000000000000000 SWAP1 DIV DUP1 PUSH4 0x161E7150 EQ PUSH2 0x40 JUMPI JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x4C JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0xAF PUSH1 0x4 DUP1 CALLDATASIZE SUB PUSH1 0x40 DUP2 LT ISZERO PUSH2 0x63 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP2 ADD SWAP1 DUP1 DUP1 CALLDATALOAD PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF AND SWAP1 PUSH1 0x20 ADD SWAP1 SWAP3 SWAP2 SWAP1 DUP1 CALLDATALOAD PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF AND SWAP1 PUSH1 0x20 ADD SWAP1 SWAP3 SWAP2 SWAP1 POP POP POP PUSH2 0xC9 JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 DUP3 ISZERO ISZERO ISZERO ISZERO DUP2 MSTORE PUSH1 0x20 ADD SWAP2 POP POP PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 RETURN JUMPDEST PUSH1 0x0 DUP2 PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF AND DUP4 PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF AND EQ ISZERO PUSH2 0x108 JUMPI PUSH1 0x1 SWAP1 POP PUSH2 0x10D JUMP JUMPDEST PUSH1 0x0 SWAP1 POP JUMPDEST SWAP3 SWAP2 POP POP JUMP INVALID LOG1 PUSH6 0x627A7A723058 KECCAK256 CALLDATALOAD DUP16 PUSH8 0xA58C115EA636B0B8 0xe5 0xc4 0xca PUSH27 0x52B8192D0F3FA98A4434D6EA04596B5D0D00290000000000000000 ",
	"sourceMap": "221:8828:0:-;;;263:110;8:9:-1;5:2;;;30:1;27;20:12;5:2;263:110:0;324:11;;:::i;:::-;;;;;;;;;;;8:9:-1;5:2;;;45:16;42:1;39;24:38;77:16;74:1;67:27;5:2;324:11:0;316:5;;:19;;;;;;;;;;;;;;;;;;221:8828;;;;;;;;;;;;:::o;:::-;;;;;;;"
}
