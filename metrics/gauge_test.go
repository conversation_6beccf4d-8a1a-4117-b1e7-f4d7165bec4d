package metrics

import (
	"testing"
)

func BenchmarkGauge(b *testing.B) {
	g := NewGauge()
	b.<PERSON>()
	for i := 0; i < b.N; i++ {
		g.Update(int64(i))
	}
}

func TestGaugeSnapshot(t *testing.T) {
	g := NewGauge()
	g.Update(int64(47))
	snapshot := g.Snapshot()
	g.Update(int64(0))
	if v := snapshot.Value(); v != 47 {
		t.<PERSON>("g.Value(): 47 != %v\n", v)
	}
}

func TestGetOrRegisterGauge(t *testing.T) {
	r := NewRegistry()
	NewRegisteredGauge("foo", r).Update(47)
	if g := GetOrRegisterGauge("foo", r); g.Snapshot().Value() != 47 {
		t.<PERSON><PERSON>(g)
	}
}
