// Code generated via abigen V2 - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.

package bindtests

import (
	"bytes"
	"errors"
	"math/big"

	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind/v2"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
)

// Reference imports to suppress errors if they are not otherwise used.
var (
	_ = bytes.Equal
	_ = errors.New
	_ = big.NewInt
	_ = common.Big1
	_ = types.BloomLookup
	_ = abi.ConvertType
)

// InputCheckerMetaData contains all meta data concerning the InputChecker contract.
var InputCheckerMetaData = bind.MetaData{
	ABI: "[{\"type\":\"function\",\"name\":\"noInput\",\"constant\":true,\"inputs\":[],\"outputs\":[]},{\"type\":\"function\",\"name\":\"namedInput\",\"constant\":true,\"inputs\":[{\"name\":\"str\",\"type\":\"string\"}],\"outputs\":[]},{\"type\":\"function\",\"name\":\"anonInput\",\"constant\":true,\"inputs\":[{\"name\":\"\",\"type\":\"string\"}],\"outputs\":[]},{\"type\":\"function\",\"name\":\"namedInputs\",\"constant\":true,\"inputs\":[{\"name\":\"str1\",\"type\":\"string\"},{\"name\":\"str2\",\"type\":\"string\"}],\"outputs\":[]},{\"type\":\"function\",\"name\":\"anonInputs\",\"constant\":true,\"inputs\":[{\"name\":\"\",\"type\":\"string\"},{\"name\":\"\",\"type\":\"string\"}],\"outputs\":[]},{\"type\":\"function\",\"name\":\"mixedInputs\",\"constant\":true,\"inputs\":[{\"name\":\"\",\"type\":\"string\"},{\"name\":\"str\",\"type\":\"string\"}],\"outputs\":[]}]",
	ID:  "e551ce092312e54f54f45ffdf06caa4cdc",
}

// InputChecker is an auto generated Go binding around an Ethereum contract.
type InputChecker struct {
	abi abi.ABI
}

// NewInputChecker creates a new instance of InputChecker.
func NewInputChecker() *InputChecker {
	parsed, err := InputCheckerMetaData.ParseABI()
	if err != nil {
		panic(errors.New("invalid ABI: " + err.Error()))
	}
	return &InputChecker{abi: *parsed}
}

// Instance creates a wrapper for a deployed contract instance at the given address.
// Use this to create the instance object passed to abigen v2 library functions Call, Transact, etc.
func (c *InputChecker) Instance(backend bind.ContractBackend, addr common.Address) *bind.BoundContract {
	return bind.NewBoundContract(addr, c.abi, backend, backend, backend)
}

// PackAnonInput is the Go binding used to pack the parameters required for calling
// the contract method with ID 0x3e708e82.  This method will panic if any
// invalid/nil inputs are passed.
//
// Solidity: function anonInput(string ) returns()
func (inputChecker *InputChecker) PackAnonInput(arg0 string) []byte {
	enc, err := inputChecker.abi.Pack("anonInput", arg0)
	if err != nil {
		panic(err)
	}
	return enc
}

// TryPackAnonInput is the Go binding used to pack the parameters required for calling
// the contract method with ID 0x3e708e82.  This method will return an error
// if any inputs are invalid/nil.
//
// Solidity: function anonInput(string ) returns()
func (inputChecker *InputChecker) TryPackAnonInput(arg0 string) ([]byte, error) {
	return inputChecker.abi.Pack("anonInput", arg0)
}

// PackAnonInputs is the Go binding used to pack the parameters required for calling
// the contract method with ID 0x28160527.  This method will panic if any
// invalid/nil inputs are passed.
//
// Solidity: function anonInputs(string , string ) returns()
func (inputChecker *InputChecker) PackAnonInputs(arg0 string, arg1 string) []byte {
	enc, err := inputChecker.abi.Pack("anonInputs", arg0, arg1)
	if err != nil {
		panic(err)
	}
	return enc
}

// TryPackAnonInputs is the Go binding used to pack the parameters required for calling
// the contract method with ID 0x28160527.  This method will return an error
// if any inputs are invalid/nil.
//
// Solidity: function anonInputs(string , string ) returns()
func (inputChecker *InputChecker) TryPackAnonInputs(arg0 string, arg1 string) ([]byte, error) {
	return inputChecker.abi.Pack("anonInputs", arg0, arg1)
}

// PackMixedInputs is the Go binding used to pack the parameters required for calling
// the contract method with ID 0xc689ebdc.  This method will panic if any
// invalid/nil inputs are passed.
//
// Solidity: function mixedInputs(string , string str) returns()
func (inputChecker *InputChecker) PackMixedInputs(arg0 string, str string) []byte {
	enc, err := inputChecker.abi.Pack("mixedInputs", arg0, str)
	if err != nil {
		panic(err)
	}
	return enc
}

// TryPackMixedInputs is the Go binding used to pack the parameters required for calling
// the contract method with ID 0xc689ebdc.  This method will return an error
// if any inputs are invalid/nil.
//
// Solidity: function mixedInputs(string , string str) returns()
func (inputChecker *InputChecker) TryPackMixedInputs(arg0 string, str string) ([]byte, error) {
	return inputChecker.abi.Pack("mixedInputs", arg0, str)
}

// PackNamedInput is the Go binding used to pack the parameters required for calling
// the contract method with ID 0x0d402005.  This method will panic if any
// invalid/nil inputs are passed.
//
// Solidity: function namedInput(string str) returns()
func (inputChecker *InputChecker) PackNamedInput(str string) []byte {
	enc, err := inputChecker.abi.Pack("namedInput", str)
	if err != nil {
		panic(err)
	}
	return enc
}

// TryPackNamedInput is the Go binding used to pack the parameters required for calling
// the contract method with ID 0x0d402005.  This method will return an error
// if any inputs are invalid/nil.
//
// Solidity: function namedInput(string str) returns()
func (inputChecker *InputChecker) TryPackNamedInput(str string) ([]byte, error) {
	return inputChecker.abi.Pack("namedInput", str)
}

// PackNamedInputs is the Go binding used to pack the parameters required for calling
// the contract method with ID 0x63c796ed.  This method will panic if any
// invalid/nil inputs are passed.
//
// Solidity: function namedInputs(string str1, string str2) returns()
func (inputChecker *InputChecker) PackNamedInputs(str1 string, str2 string) []byte {
	enc, err := inputChecker.abi.Pack("namedInputs", str1, str2)
	if err != nil {
		panic(err)
	}
	return enc
}

// TryPackNamedInputs is the Go binding used to pack the parameters required for calling
// the contract method with ID 0x63c796ed.  This method will return an error
// if any inputs are invalid/nil.
//
// Solidity: function namedInputs(string str1, string str2) returns()
func (inputChecker *InputChecker) TryPackNamedInputs(str1 string, str2 string) ([]byte, error) {
	return inputChecker.abi.Pack("namedInputs", str1, str2)
}

// PackNoInput is the Go binding used to pack the parameters required for calling
// the contract method with ID 0x53539029.  This method will panic if any
// invalid/nil inputs are passed.
//
// Solidity: function noInput() returns()
func (inputChecker *InputChecker) PackNoInput() []byte {
	enc, err := inputChecker.abi.Pack("noInput")
	if err != nil {
		panic(err)
	}
	return enc
}

// TryPackNoInput is the Go binding used to pack the parameters required for calling
// the contract method with ID 0x53539029.  This method will return an error
// if any inputs are invalid/nil.
//
// Solidity: function noInput() returns()
func (inputChecker *InputChecker) TryPackNoInput() ([]byte, error) {
	return inputChecker.abi.Pack("noInput")
}
